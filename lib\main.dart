import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:device_preview/device_preview.dart';
import 'package:flutter/foundation.dart';
import 'screens/home_screen.dart';

void main() {
  runApp(
    DevicePreview(
      enabled: !kReleaseMode, // Only enable in debug mode
      builder: (context) => const AndroidTVApp(),
    ),
  );
}

// Alternative main function for production (without DevicePreview)
// void main() {
//   WidgetsFlutterBinding.ensureInitialized();
//   SystemChrome.setPreferredOrientations([
//     DeviceOrientation.landscapeLeft,
//     DeviceOrientation.landscapeRight,
//   ]).then((_) {
//     runApp(const AndroidTVApp());
//   });
// }

class AndroidTVApp extends StatelessWidget {
  const AndroidTVApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'KisanKonnect Android TV',
      
      // Device Preview configuration
      useInheritedMediaQuery: true,
      locale: DevicePreview.locale(context),
      builder: DevicePreview.appBuilder,
      
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.green,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
        
        // TV-optimized theme
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            textStyle: const TextStyle(fontSize: 18),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        
        dropdownMenuTheme: const DropdownMenuThemeData(
          textStyle: TextStyle(fontSize: 18),
        ),
        
        // Additional TV-specific theme configurations
        cardTheme: CardTheme(
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        
        // Focus theme for TV navigation
        focusTheme: FocusThemeData(
          glowFactor: 0.0,
          glowColor: Colors.transparent,
        ),
        
        // Enhanced button themes for TV
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            textStyle: const TextStyle(fontSize: 16),
          ),
        ),
        
        // Snackbar theme for better visibility on TV
        snackBarTheme: const SnackBarThemeData(
          behavior: SnackBarBehavior.floating,
          margin: EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
      ),
      
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// Custom device definitions for Android TV testing
class TVDevices {
  static const List<DeviceInfo> tvDevices = [
    // Standard Android TV sizes
    DeviceInfo(
      identifier: DeviceType.unknown,
      name: 'Android TV 32"',
      pixelRatio: 1.0,
      frameSize: Size(1920, 1080),
      screenSize: Size(1920, 1080),
      safeAreas: EdgeInsets.zero,
      rotatedSafeAreas: EdgeInsets.zero,
      screenPath: '',
      framePath: '',
    ),
    
    DeviceInfo(
      identifier: DeviceType.unknown,
      name: 'Android TV 43"',
      pixelRatio: 1.0,
      frameSize: Size(1920, 1080),
      screenSize: Size(1920, 1080),
      safeAreas: EdgeInsets.zero,
      rotatedSafeAreas: EdgeInsets.zero,
      screenPath: '',
      framePath: '',
    ),
    
    DeviceInfo(
      identifier: DeviceType.unknown,
      name: 'Android TV 55" 4K',
      pixelRatio: 2.0,
      frameSize: Size(3840, 2160),
      screenSize: Size(1920, 1080),
      safeAreas: EdgeInsets.zero,
      rotatedSafeAreas: EdgeInsets.zero,
      screenPath: '',
      framePath: '',
    ),
    
    DeviceInfo(
      identifier: DeviceType.unknown,
      name: 'Android TV 75" 4K',
      pixelRatio: 2.0,
      frameSize: Size(3840, 2160),
      screenSize: Size(1920, 1080),
      safeAreas: EdgeInsets.zero,
      rotatedSafeAreas: EdgeInsets.zero,
      screenPath: '',
      framePath: '',
    ),
  ];
}

// Extension to add TV-specific device preview configurations
extension TVDevicePreview on DevicePreview {
  static DevicePreview tvPreview({
    required WidgetBuilder builder,
    bool enabled = true,
  }) {
    return DevicePreview(
      enabled: enabled,
      builder: builder,
      devices: [
        ...Devices.all,
        // Add custom TV devices here if needed
      ],
      tools: const [
        DeviceSection(),
        SystemSection(),
        AccessibilitySection(),
        SettingsSection(),
      ],
    );
  }
}