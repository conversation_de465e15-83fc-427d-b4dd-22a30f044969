import 'package:flutter/material.dart';
import '../models/farm_store_models.dart';
import '../services/api_service.dart';
import 'video_player_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<FarmStore> _farmStores = [];
  List<ScreenOption> _screens = [];
  FarmStore? _selectedFarm;
  ScreenOption? _selectedScreen;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadFarmStoresAndScreens();
  }

  Future<void> _loadFarmStoresAndScreens() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.getFarmStoresAndScreens();
      
      setState(() {
        _farmStores = response.farmStores;
        _screens = response.screens;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _playMedia() async {
    if (_selectedFarm == null || _selectedScreen == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select both farm and screen'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final mediaResponse = await ApiService.getMediaItems(
        farmId: _selectedFarm!.farmID,
        screenId: int.parse(_selectedScreen!.id),
      );

      if (mediaResponse.mediaItems.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No media found for selected farm and screen'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Navigate to video player screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => VideoPlayerScreen(
            mediaItems: mediaResponse.mediaItems,
            farmName: _selectedFarm!.farmName,
            screenName: _selectedScreen!.name,
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading media: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B5E20),
              Color(0xFF2E7D32),
              Color(0xFF388E3C),
            ],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: IntrinsicHeight(
                    child: _buildResponsiveContent(constraints),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveContent(BoxConstraints constraints) {
    final screenWidth = constraints.maxWidth;
    final screenHeight = constraints.maxHeight;
    
    // Calculate responsive dimensions
    final isLargeScreen = screenWidth > 1200;
    final isMediumScreen = screenWidth > 800 && screenWidth <= 1200;
    final isSmallScreen = screenWidth <= 800;
    
    // Responsive padding
    final horizontalPadding = isLargeScreen ? 64.0 : 
                             isMediumScreen ? 32.0 : 16.0;
    final verticalPadding = isLargeScreen ? 48.0 : 
                           isMediumScreen ? 24.0 : 16.0;
    
    // Responsive font sizes
    final titleSize = isLargeScreen ? 48.0 : 
                     isMediumScreen ? 36.0 : 28.0;
    final subtitleSize = isLargeScreen ? 24.0 : 
                        isMediumScreen ? 20.0 : 16.0;
    
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Header with responsive sizing
          _buildHeader(titleSize, subtitleSize),
          
          SizedBox(height: isLargeScreen ? 64 : 32),
          
          // Content area
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _errorMessage != null
                    ? _buildErrorState()
                    : _buildSelectionForm(constraints),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(double titleSize, double subtitleSize) {
    return Column(
      children: [
        FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            'KisanKonnect Android TV',
            style: TextStyle(
              fontSize: titleSize,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 16),
        FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            'Select Farm Store and Screen to Play Media',
            style: TextStyle(
              fontSize: subtitleSize,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Colors.white,
            strokeWidth: 6,
          ),
          const SizedBox(height: 24),
          FittedBox(
            child: Text(
              'Loading farm stores...',
              style: TextStyle(
                fontSize: MediaQuery.of(context).size.width > 800 ? 20 : 16,
                color: Colors.white70,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Error: $_errorMessage',
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadFarmStoresAndScreens,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionForm(BoxConstraints constraints) {
    final screenWidth = constraints.maxWidth;
    final isLargeScreen = screenWidth > 1200;
    final isMediumScreen = screenWidth > 800 && screenWidth <= 1200;
    
    // Calculate card width based on screen size
    final cardWidth = isLargeScreen ? 900.0 : 
                     isMediumScreen ? 700.0 : 
                     screenWidth * 0.9;
    
    // Calculate internal padding
    final cardPadding = isLargeScreen ? 48.0 : 
                       isMediumScreen ? 32.0 : 24.0;
    
    return Center(
      child: SingleChildScrollView(
        child: Container(
          width: cardWidth,
          constraints: BoxConstraints(
            maxWidth: screenWidth * 0.95,
            minHeight: constraints.maxHeight * 0.4,
          ),
          child: Card(
            elevation: 8,
            child: Padding(
              padding: EdgeInsets.all(cardPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Farm Store Dropdown
                  _buildDropdown<FarmStore>(
                    label: 'Select Farm Store',
                    value: _selectedFarm,
                    items: _farmStores,
                    onChanged: (farm) => setState(() => _selectedFarm = farm),
                    itemBuilder: (farm) => farm.farmName,
                    constraints: constraints,
                  ),
                  
                  SizedBox(height: isLargeScreen ? 32 : 24),

                  // Screen Dropdown
                  _buildDropdown<ScreenOption>(
                    label: 'Select Screen',
                    value: _selectedScreen,
                    items: _screens,
                    onChanged: (screen) => setState(() => _selectedScreen = screen),
                    itemBuilder: (screen) => screen.name,
                    constraints: constraints,
                  ),
                  
                  SizedBox(height: isLargeScreen ? 48 : 32),

                  // Play Button
                  _buildPlayButton(constraints),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayButton(BoxConstraints constraints) {
    final isLargeScreen = constraints.maxWidth > 1200;
    final buttonHeight = isLargeScreen ? 64.0 : 56.0;
    final fontSize = isLargeScreen ? 24.0 : 20.0;
    final iconSize = isLargeScreen ? 32.0 : 28.0;
    
    return SizedBox(
      width: double.infinity,
      height: buttonHeight,
      child: ElevatedButton.icon(
        onPressed: _selectedFarm != null && _selectedScreen != null
            ? _playMedia
            : null,
        icon: Icon(Icons.play_arrow, size: iconSize),
        label: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            'Play Media',
            style: TextStyle(fontSize: fontSize),
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T? value,
    required List<T> items,
    required ValueChanged<T?> onChanged,
    required String Function(T) itemBuilder,
    required BoxConstraints constraints,
  }) {
    final isLargeScreen = constraints.maxWidth > 1200;
    final labelSize = isLargeScreen ? 18.0 : 16.0;
    final dropdownTextSize = isLargeScreen ? 16.0 : 14.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: labelSize,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              hint: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  'Choose $label',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: dropdownTextSize,
                  ),
                ),
              ),
              isExpanded: true,
              style: TextStyle(
                fontSize: dropdownTextSize,
                color: Colors.black87,
              ),
              dropdownColor: Colors.white,
              menuMaxHeight: constraints.maxHeight * 0.4,
              items: items.map((item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      itemBuilder(item),
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: dropdownTextSize,
                      ),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }
}