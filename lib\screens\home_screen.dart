import 'package:flutter/material.dart';
import '../models/farm_store_models.dart';
import '../services/api_service.dart';
import 'video_player_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<FarmStore> _farmStores = [];
  List<ScreenOption> _screens = [];
  FarmStore? _selectedFarm;
  ScreenOption? _selectedScreen;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadFarmStoresAndScreens();
  }

  Future<void> _loadFarmStoresAndScreens() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.getFarmStoresAndScreens();
      
      setState(() {
        _farmStores = response.farmStores;
        _screens = response.screens;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _playMedia() async {
    if (_selectedFarm == null || _selectedScreen == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select both farm and screen'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final mediaResponse = await ApiService.getMediaItems(
        farmId: _selectedFarm!.farmID,
        screenId: int.parse(_selectedScreen!.id),
      );

      if (mediaResponse.mediaItems.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No media found for selected farm and screen'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Navigate to video player screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => VideoPlayerScreen(
            mediaItems: mediaResponse.mediaItems,
            farmName: _selectedFarm!.farmName,
            screenName: _selectedScreen!.name,
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading media: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B5E20),
              Color(0xFF2E7D32),
              Color(0xFF388E3C),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Header
                const Text(
                  'KisanKonnect Android TV',
                  style: TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Select Farm Store and Screen to Play Media',
                  style: TextStyle(
                    fontSize: 24,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 64),

                // Content
                Expanded(
                  child: _isLoading
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 6,
                              ),
                              SizedBox(height: 24),
                              Text(
                                'Loading farm stores...',
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        )
                      : _errorMessage != null
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Error: $_errorMessage',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      color: Colors.white,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 24),
                                  ElevatedButton(
                                    onPressed: _loadFarmStoresAndScreens,
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            )
                          : _buildSelectionForm(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionForm() {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 800),
        child: Card(
          elevation: 8,
          child: Padding(
            padding: const EdgeInsets.all(48.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Farm Store Dropdown
                _buildDropdown<FarmStore>(
                  label: 'Select Farm Store',
                  value: _selectedFarm,
                  items: _farmStores,
                  onChanged: (farm) => setState(() => _selectedFarm = farm),
                  itemBuilder: (farm) => farm.farmName,
                ),
                const SizedBox(height: 32),

                // Screen Dropdown
                _buildDropdown<ScreenOption>(
                  label: 'Select Screen',
                  value: _selectedScreen,
                  items: _screens,
                  onChanged: (screen) => setState(() => _selectedScreen = screen),
                  itemBuilder: (screen) => screen.name,
                ),
                const SizedBox(height: 48),

                // Play Button
                SizedBox(
                  width: double.infinity,
                  height: 64,
                  child: ElevatedButton.icon(
                    onPressed: _selectedFarm != null && _selectedScreen != null
                        ? _playMedia
                        : null,
                    icon: const Icon(Icons.play_arrow, size: 32),
                    label: const Text(
                      'Play Media',
                      style: TextStyle(fontSize: 24),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T? value,
    required List<T> items,
    required ValueChanged<T?> onChanged,
    required String Function(T) itemBuilder,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              hint: Text(
                'Choose $label',
                style: const TextStyle(color: Colors.white70),
              ),
              isExpanded: true,
              style: const TextStyle(fontSize: 16, color: Colors.white),
              dropdownColor: Colors.grey[800],
              items: items.map((item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: Text(
                    itemBuilder(item),
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }
}
