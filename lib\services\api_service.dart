import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/farm_store_models.dart';

class ApiService {
  static const String baseUrl = 'http://knet.kisankonnect.com/BravoUAT/api/FarmStore';

  /// Fetch farm stores and screen options
  static Future<FarmStoreResponse> getFarmStoresAndScreens() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/FE_FarmStoreAndScreen'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return FarmStoreResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load farm stores: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching farm stores: $e');
    }
  }

  /// Fetch media items for a specific farm and screen
  static Future<MediaResponse> getMediaItems({
    required int farmId,
    required int screenId,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/FE_FarmStoreMedia?FarmID=$farmId&Screen=$screenId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return MediaResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load media: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching media: $e');
    }
  }
}
