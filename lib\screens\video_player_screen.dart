import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'dart:async';
import '../models/farm_store_models.dart';

class VideoPlayerScreen extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final String farmName;
  final String screenName;

  const VideoPlayerScreen({
    super.key,
    required this.mediaItems,
    required this.farmName,
    required this.screenName,
  });

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  VideoPlayerController? _controller;
  int _currentIndex = 0;
  Timer? _timer;
  bool _isLoading = true;
  String? _errorMessage;
  bool _showControls = true;
  Timer? _controlsTimer;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _startControlsTimer();
  }

  void _startControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() => _showControls = false);
      }
    });
  }

  void _showControlsTemporarily() {
    setState(() => _showControls = true);
    _startControlsTimer();
  }

  Future<void> _initializePlayer() async {
    if (widget.mediaItems.isEmpty) return;

    final currentMedia = widget.mediaItems[_currentIndex];

    // Skip non-video
    if (!currentMedia.isVideo) {
      _showImageAndMoveToNext();
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      _controller?.dispose();
      _controller = VideoPlayerController.networkUrl(Uri.parse(currentMedia.mediaURL));

      await _controller!.initialize();

      // Autoplay
      await _controller!.play();

      // Timer to move to next video
      _timer?.cancel();
      _timer = Timer(Duration(minutes: currentMedia.minutes), () {
        _moveToNext();
      });

      _controller!.addListener(() {
        if (_controller!.value.position >= _controller!.value.duration) {
          _moveToNext();
        }
      });

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading video: $e';
        _isLoading = false;
      });
    }
  }

  void _showImageAndMoveToNext() {
    final currentMedia = widget.mediaItems[_currentIndex];

    setState(() {
      _isLoading = false;
      _errorMessage = null;
    });

    _timer?.cancel();
    _timer = Timer(Duration(minutes: currentMedia.minutes), _moveToNext);
  }

  void _moveToNext() {
    if (_currentIndex < widget.mediaItems.length - 1) {
      setState(() => _currentIndex++);
    } else {
      setState(() => _currentIndex = 0); // Loop
    }
    _initializePlayer();
  }

  void _moveToPrevious() {
    if (_currentIndex > 0) {
      setState(() => _currentIndex--);
    } else {
      setState(() => _currentIndex = widget.mediaItems.length - 1);
    }
    _initializePlayer();
  }

  @override
  void dispose() {
    _controller?.dispose();
    _timer?.cancel();
    _controlsTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _showControlsTemporarily,
        child: Stack(
          children: [
            // Media
            Center(child: _buildMediaContent()),

            // Overlay
            if (_showControls)
              Positioned.fill(
                child: Column(
                  children: [
                    SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(Icons.arrow_back, color: Colors.white, size: 32),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(widget.farmName,
                                      style: const TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
                                  Text(widget.screenName,
                                      style: const TextStyle(color: Colors.white70, fontSize: 18)),
                                ],
                              ),
                            ),
                            Text('${_currentIndex + 1} / ${widget.mediaItems.length}',
                                style: const TextStyle(color: Colors.white, fontSize: 18)),
                          ],
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Bottom controls
                    Container(
                      width: double.infinity,
                      color: Colors.black.withOpacity(0.6),
                      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 32),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            onPressed: _moveToPrevious,
                            icon: const Icon(Icons.skip_previous, color: Colors.white, size: 48),
                          ),
                          const SizedBox(width: 32),
                          IconButton(
                            onPressed: () {
                              if (_controller?.value.isPlaying == true) {
                                _controller?.pause();
                              } else {
                                _controller?.play();
                              }
                              setState(() {});
                            },
                            icon: Icon(
                              _controller?.value.isPlaying == true ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                              size: 64,
                            ),
                          ),
                          const SizedBox(width: 32),
                          IconButton(
                            onPressed: _moveToNext,
                            icon: const Icon(Icons.skip_next, color: Colors.white, size: 48),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaContent() {
    if (_isLoading) {
      return const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white, strokeWidth: 6),
          SizedBox(height: 24),
          Text('Loading media...', style: TextStyle(color: Colors.white, fontSize: 20)),
        ],
      );
    }

    if (_errorMessage != null) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          Text(_errorMessage!, style: const TextStyle(color: Colors.white, fontSize: 18), textAlign: TextAlign.center),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _initializePlayer,
            child: const Text('Retry'),
          ),
        ],
      );
    }

    final currentMedia = widget.mediaItems[_currentIndex];

    if (currentMedia.isVideo && _controller != null && _controller!.value.isInitialized) {
      return AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: VideoPlayer(_controller!),
      );
    } else if (currentMedia.isImage) {
      return Image.network(
        currentMedia.mediaURL,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) => const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.broken_image, color: Colors.white54, size: 64),
            SizedBox(height: 16),
            Text('Failed to load image', style: TextStyle(color: Colors.white54, fontSize: 18)),
          ],
        ),
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return const CircularProgressIndicator(color: Colors.white);
        },
      );
    }

    return const Text(
      'Unsupported media type',
      style: TextStyle(color: Colors.white, fontSize: 18),
    );
  }
}
