class FarmStore {
  final int farmID;
  final String farmName;

  FarmStore({
    required this.farmID,
    required this.farmName,
  });

  factory FarmStore.fromJson(Map<String, dynamic> json) {
    return FarmStore(
      farmID: json['FarmID'],
      farmName: json['FarmName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'FarmID': farmID,
      'FarmName': farmName,
    };
  }
}

class ScreenOption {
  final String id;
  final String name;

  ScreenOption({
    required this.id,
    required this.name,
  });

  factory ScreenOption.fromJson(Map<String, dynamic> json) {
    final entry = json.entries.first;
    return ScreenOption(
      id: entry.key,
      name: entry.value,
    );
  }
}

class FarmStoreResponse {
  final String status;
  final String message;
  final List<FarmStore> farmStores;
  final List<ScreenOption> screens;

  FarmStoreResponse({
    required this.status,
    required this.message,
    required this.farmStores,
    required this.screens,
  });

  factory FarmStoreResponse.fromJson(Map<String, dynamic> json) {
    return FarmStoreResponse(
      status: json['status'],
      message: json['MSG'],
      farmStores: (json['FE_FarmStore'] as List)
          .map((item) => FarmStore.fromJson(item))
          .toList(),
      screens: (json['screensArray'] as List)
          .map((item) => ScreenOption.fromJson(item))
          .toList(),
    );
  }
}

class MediaItem {
  final int mediaType; // 1 for video, 2 for image
  final String mediaURL;
  final int minutes;

  MediaItem({
    required this.mediaType,
    required this.mediaURL,
    required this.minutes,
  });

  factory MediaItem.fromJson(Map<String, dynamic> json) {
    return MediaItem(
      mediaType: json['MediaType'],
      mediaURL: json['MediaURL'],
      minutes: json['Minutes'],
    );
  }

  bool get isVideo => mediaType == 1;
  bool get isImage => mediaType == 2;
}

class MediaResponse {
  final String status;
  final String message;
  final List<MediaItem> mediaItems;

  MediaResponse({
    required this.status,
    required this.message,
    required this.mediaItems,
  });

  factory MediaResponse.fromJson(Map<String, dynamic> json) {
    return MediaResponse(
      status: json['status'],
      message: json['MSG'],
      mediaItems: (json['FE_FarmStoreMedia'] as List)
          .map((item) => MediaItem.fromJson(item))
          .toList(),
    );
  }
}
